import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../controllers/order_list_controller.dart';
import '../models/order_model.dart';

class OrderListScreen extends GetView<OrderListController> {
  const OrderListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: Row(
        children: [
          // Left Sidebar
          _buildSidebar(),

          // Main Content
          Expanded(child: _buildMainContent()),
        ],
      ),
    );
  }

  Widget _buildSidebar() {
    return Container(
      width: MySize.size200,
      color: AppColors.primaryColor,
      child: Column(
        children: [
          // Logo Section
          Container(
            padding: EdgeInsets.all(MySize.size20),
            child: Row(
              children: [
                Image.asset(
                  'assets/png/logo.png',
                  width: MySize.size32,
                  height: MySize.size32,
                ),
                SizedBox(width: MySize.size8),
                Expanded(
                  child: Text(
                    'Packagingwala',
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.blackColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // Navigation Items
          Expanded(
            child: Obx(
              () => Column(
                children: [
                  _buildNavItem(
                    icon: 'home',
                    label: 'Dashboard',
                    isSelected: controller.selectedNavIndex.value == 0,
                    onTap: () => controller.selectNavItem(0),
                  ),
                  _buildNavItem(
                    icon: 'person',
                    label: 'Customers List',
                    isSelected: controller.selectedNavIndex.value == 1,
                    onTap: () => controller.selectNavItem(1),
                  ),
                  _buildNavItem(
                    icon: 'shopping_cart',
                    label: 'Orders List',
                    isSelected: controller.selectedNavIndex.value == 2,
                    onTap: () => controller.selectNavItem(2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: MySize.size12,
          vertical: MySize.size4,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size16,
          vertical: MySize.size12,
        ),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
        child: Row(
          children: [
            PlatformIcon(
              iconName: icon,
              size: MySize.size20,
              color: isSelected ? AppColors.blackColor : Colors.white,
            ),
            SizedBox(width: MySize.size12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? AppColors.blackColor : Colors.white,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),

          SizedBox(height: MySize.size24),

          // Orders Table
          Expanded(child: _buildOrdersTable()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title and Search Row
        Row(
          children: [
            // Title Section
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Order List',
                    style: TextStyle(
                      fontSize: MySize.size24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: MySize.size4),
                  Text(
                    'Stay on top of every order. View statuses, check package details, and take quick actions anytime.',
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: MySize.size16),

            // Search Bar
            Expanded(
              flex: 1,
              child: Container(
                constraints: BoxConstraints(maxWidth: 350, minWidth: 250),
                child: CustomTextField(
                  controller: controller.searchController,
                  hintText: 'Search by Order ID/Customer Name or phone',
                  onChanged: controller.onSearchChanged,
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(MySize.size12),
                    child: PlatformIcon(
                      iconName: 'search',
                      size: MySize.size20,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  fillColor: Colors.white,
                  borderColor: AppColors.borderColor,
                  borderRadius: MySize.size20,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: MySize.size16,
                    vertical: MySize.size10,
                  ),
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: MySize.size16),

        // Action Buttons Row
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Export Button
            GestureDetector(
              onTap: controller.onExportTap,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size8,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: AppColors.borderColor),
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    PlatformIcon(
                      iconName: 'download',
                      size: MySize.size16,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(width: MySize.size8),
                    Text(
                      'Export',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(width: MySize.size12),

            // Add Order Button
            GestureDetector(
              onTap: controller.onAddOrderTap,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size16,
                  vertical: MySize.size8,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(MySize.size8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    PlatformIcon(
                      iconName: 'add',
                      size: MySize.size16,
                      color: AppColors.blackColor,
                    ),
                    SizedBox(width: MySize.size8),
                    Text(
                      'Add Order',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.blackColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOrdersTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          _buildTableHeader(),

          // Table Content
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              final orders = controller.paginatedOrders;
              if (orders.isEmpty) {
                return const Center(
                  child: Text('No orders found'),
                );
              }

              return ListView.builder(
                itemCount: orders.length,
                itemBuilder: (context, index) {
                  return _buildTableRow(orders[index], index);
                },
              );
            }),
          ),

          // Pagination
          _buildPagination(),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'ORDER ID',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'CUSTOMER',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'ORDER DATE',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'DELIVERY DATE',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'ITEMS',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'AMOUNT',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'STATUS',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'ACTION',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(OrderModel order, int index) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Order ID
          Expanded(
            flex: 2,
            child: Text(
              order.orderId,
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Customer
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.customerName,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: MySize.size2),
                Text(
                  order.customerPhone,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          // Order Date
          Expanded(
            flex: 2,
            child: Text(
              _formatDate(order.orderDate),
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Delivery Date
          Expanded(
            flex: 2,
            child: Text(
              _formatDate(order.deliveryDate),
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Items
          Expanded(
            flex: 1,
            child: Row(
              children: [
                Container(
                  width: MySize.size16,
                  height: MySize.size16,
                  decoration: BoxDecoration(
                    color: AppColors.borderColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.inventory_2_outlined,
                    size: MySize.size10,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(width: MySize.size8),
                Text(
                  '${order.itemCount} Items',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          // Amount
          Expanded(
            flex: 1,
            child: Text(
              '₹ ${_formatAmount(order.amount)}',
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Status
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: MySize.size12,
                vertical: MySize.size6,
              ),
              decoration: BoxDecoration(
                color: controller.getStatusColor(order.status).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(MySize.size16),
              ),
              child: Text(
                order.status,
                style: TextStyle(
                  fontSize: MySize.size12,
                  fontWeight: FontWeight.w500,
                  color: controller.getStatusColor(order.status),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          // Action
          Expanded(
            flex: 1,
            child: Center(
              child: GestureDetector(
                onTap: () => controller.onOrderActionTap(order),
                child: Container(
                  padding: EdgeInsets.all(MySize.size8),
                  child: PlatformIcon(
                    iconName: 'more_vert',
                    size: MySize.size16,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPagination() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      child: Obx(() {
        final totalPages = controller.totalPages;
        final currentPage = controller.currentPage.value;

        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Previous Button
            GestureDetector(
              onTap: currentPage > 1 ? () => controller.changePage(currentPage - 1) : null,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size12,
                  vertical: MySize.size8,
                ),
                decoration: BoxDecoration(
                  color: currentPage > 1 ? Colors.white : AppColors.borderColor,
                  border: Border.all(color: AppColors.borderColor),
                  borderRadius: BorderRadius.circular(MySize.size6),
                ),
                child: PlatformIcon(
                  iconName: 'chevron_left',
                  size: MySize.size16,
                  color: currentPage > 1 ? AppColors.textPrimary : AppColors.textSecondary,
                ),
              ),
            ),

            SizedBox(width: MySize.size8),

            // Page Numbers
            ...List.generate(totalPages > 5 ? 5 : totalPages, (index) {
              int pageNumber = index + 1;
              if (totalPages > 5 && currentPage > 3) {
                pageNumber = currentPage - 2 + index;
                if (pageNumber > totalPages) pageNumber = totalPages - 4 + index;
              }

              return GestureDetector(
                onTap: () => controller.changePage(pageNumber),
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: MySize.size2),
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.size12,
                    vertical: MySize.size8,
                  ),
                  decoration: BoxDecoration(
                    color: pageNumber == currentPage ? AppColors.primaryColor : Colors.white,
                    border: Border.all(color: AppColors.borderColor),
                    borderRadius: BorderRadius.circular(MySize.size6),
                  ),
                  child: Text(
                    pageNumber.toString(),
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color: pageNumber == currentPage ? AppColors.blackColor : AppColors.textPrimary,
                    ),
                  ),
                ),
              );
            }),

            SizedBox(width: MySize.size8),

            // Next Button
            GestureDetector(
              onTap: currentPage < totalPages ? () => controller.changePage(currentPage + 1) : null,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size12,
                  vertical: MySize.size8,
                ),
                decoration: BoxDecoration(
                  color: currentPage < totalPages ? Colors.white : AppColors.borderColor,
                  border: Border.all(color: AppColors.borderColor),
                  borderRadius: BorderRadius.circular(MySize.size6),
                ),
                child: PlatformIcon(
                  iconName: 'chevron_right',
                  size: MySize.size16,
                  color: currentPage < totalPages ? AppColors.textPrimary : AppColors.textSecondary,
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  String _formatDate(DateTime date) {
    const months = [
      'JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN',
      'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'
    ];
    return '${months[date.month - 1]} ${date.day.toString().padLeft(2, '0')} ${date.year}';
  }

  String _formatAmount(double amount) {
    if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    }
    return amount.toStringAsFixed(0);
  }
}