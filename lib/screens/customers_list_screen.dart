import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/smart_svg_icon.dart';
import '../controllers/customers_list_controller.dart';
import '../models/customer_model.dart';

class CustomersListScreen extends GetView<CustomersListController> {
  const CustomersListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: Row(
        children: [
          // Left Sidebar
          _buildSidebar(),

          // Main Content
          Expanded(child: _buildMainContent()),
        ],
      ),
    );
  }

 Widget _buildSidebar() {
    return Container(
      width: MySize.size200,
      color: AppColors.primaryColor,
      child: Column(
        children: [
          // Logo Section
          Container(
            padding: EdgeInsets.all(MySize.size20),
            child: Column(
              children: [
                SmartIcon(
                  assetPath: 'assets/icons/logo_icon.svg',
                  height: MySize.size70,
                  width: MySize.size129,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),

          // Navigation Items
          Expanded(
            child: Obx(
              () => Column(
                children: [
                  _buildNavItem(
                    icon: 'home',
                    label: 'Dashboard',
                    isSelected: controller.selectedNavIndex.value == 0,
                    onTap: () => controller.selectNavItem(0),
                  ),
                  _buildNavItem(
                    icon: 'person',
                    label: 'Customers List',
                    isSelected: controller.selectedNavIndex.value == 1,
                    onTap: () => controller.selectNavItem(1),
                  ),
                  _buildNavItem(
                    icon: 'shopping_cart',
                    label: 'Orders List',
                    isSelected: controller.selectedNavIndex.value == 2,
                    onTap: () => controller.selectNavItem(2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size4,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: ListTile(
        leading: PlatformIcon(
          iconName: icon,
          size: MySize.size20,
          color: isSelected ? AppColors.blackColor : Colors.white,
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? AppColors.blackColor : Colors.white,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      padding: EdgeInsets.all(MySize.size24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top Search Bar
          _buildTopSearchBar(),

          SizedBox(height: MySize.size16),

          // Divider
          Divider(
            color: AppColors.borderColor,
            thickness: 1,
            height: 1,
          ),

          SizedBox(height: MySize.size24),

          // Header and Action Bar in same row
          _buildHeaderAndActionRow(),

          SizedBox(height: MySize.size24),

          // Customers Table
          Expanded(child: _buildCustomersTable()),
        ],
      ),
    );
  }

  Widget _buildTopSearchBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(
          width: 300,
          child: CustomTextField(
            controller: TextEditingController(), // Separate controller for top search
            hintText: 'Search Orders',
            prefixIcon: Padding(
              padding: EdgeInsets.all(MySize.size12),
              child: PlatformIcon(
                iconName: 'search',
                size: MySize.size20,
                color: AppColors.textSecondary,
              ),
            ),
            fillColor: Colors.white,
            borderColor: AppColors.borderColor,
            borderRadius: MySize.size20,
            contentPadding: EdgeInsets.symmetric(
              horizontal: MySize.size16,
              vertical: MySize.size10,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderAndActionRow() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header Section (Left side)
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Customers',
                style: TextStyle(
                  fontSize: MySize.size24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: MySize.size4),
              Text(
                'Manage your customer base and their order history.',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),

        SizedBox(width: MySize.size24),

        // Add Customer Button (Right side)
        GestureDetector(
          onTap: controller.onAddCustomerTap,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: MySize.size16,
              vertical: MySize.size12,
            ),
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                PlatformIcon(
                  iconName: 'add',
                  size: MySize.size16,
                  color: AppColors.blackColor,
                ),
                SizedBox(width: MySize.size8),
                Text(
                  'Add Customer',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.blackColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomersTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Table Header
          _buildTableHeader(),

          // Table Content
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              final customers = controller.paginatedCustomers;
              if (customers.isEmpty) {
                return const Center(
                  child: Text('No customers found'),
                );
              }

              return ListView.builder(
                itemCount: customers.length,
                itemBuilder: (context, index) {
                  return _buildTableRow(customers[index], index);
                },
              );
            }),
          ),

          // Pagination
          _buildPagination(),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Name
          Expanded(
            flex: 3,
            child: Text(
              'NAME',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          // Phone Number
          Expanded(
            flex: 2,
            child: Text(
              'Phone Number',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          // Location
          Expanded(
            flex: 2,
            child: Text(
              'LOCATION',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          // Orders
          Expanded(
            flex: 1,
            child: Text(
              'ORDERS',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
            ),
          ),
          // Action
          Expanded(
            flex: 1,
            child: Text(
              'ACTION',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
                letterSpacing: 0.5,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(CustomerModel customer, int index) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Name
          Expanded(
            flex: 3,
            child: Text(
              customer.name,
              style: TextStyle(
                fontSize: MySize.size14,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Phone Number
          Expanded(
            flex: 2,
            child: Text(
              customer.phoneNumber,
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Location
          Expanded(
            flex: 2,
            child: Text(
              customer.location,
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Orders
          Expanded(
            flex: 1,
            child: Text(
              '${customer.orderCount} Orders',
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          // Action
          Expanded(
            flex: 1,
            child: Center(
              child: GestureDetector(
                onTap: () => controller.onCustomerActionTap(customer),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.size12,
                    vertical: MySize.size6,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(MySize.size16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      PlatformIcon(
                        iconName: 'visibility',
                        size: MySize.size14,
                        color: AppColors.blackColor,
                      ),
                      SizedBox(width: MySize.size4),
                      Text(
                        'View',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          fontWeight: FontWeight.w500,
                          color: AppColors.blackColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPagination() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size20,
        vertical: MySize.size16,
      ),
      child: Obx(() {
        final totalPages = controller.totalPages;
        final currentPage = controller.currentPage.value;

        return Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            // Previous Button
            GestureDetector(
              onTap: currentPage > 1 ? () => controller.changePage(currentPage - 1) : null,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size12,
                  vertical: MySize.size8,
                ),

                child: PlatformIcon(
                  iconName: 'chevron_left',
                  size: MySize.size16,
                  color: currentPage > 1 ? AppColors.textPrimary : AppColors.textSecondary,
                ),
              ),
            ),

            SizedBox(width: MySize.size4),

            // Page Numbers
            ...List.generate(totalPages > 5 ? 5 : totalPages, (index) {
              int pageNumber = index + 1;
              if (totalPages > 5 && currentPage > 3) {
                pageNumber = currentPage - 2 + index;
                if (pageNumber > totalPages) pageNumber = totalPages - 4 + index;
              }

              return GestureDetector(
                onTap: () => controller.changePage(pageNumber),
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: MySize.size2),
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.size12,
                    vertical: MySize.size8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: AppColors.primaryColor),
                    borderRadius: BorderRadius.circular(MySize.size6),
                  ),
                  child: Text(
                    pageNumber.toString(),
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                      color: pageNumber == currentPage ? AppColors.blackColor : AppColors.textPrimary,
                    ),
                  ),
                ),
              );
            }),

            SizedBox(width: MySize.size4),

            // Next Button
            GestureDetector(
              onTap: currentPage < totalPages ? () => controller.changePage(currentPage + 1) : null,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size12,
                  vertical: MySize.size8,
                ),

                child: PlatformIcon(
                  iconName: 'chevron_right',
                  size: MySize.size16,
                  color: currentPage < totalPages ? AppColors.textPrimary : AppColors.textSecondary,
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}