import 'package:get/get.dart';
import '../screens/login_screen.dart';
import '../screens/dashboard_screen.dart';
import '../screens/customers_list_screen.dart';
import '../screens/order_list_screen.dart';
import '../controllers/login_controller.dart';
import '../controllers/dashboard_controller.dart';
import '../controllers/customers_list_controller.dart';
import '../controllers/order_list_controller.dart';
import 'app_routes.dart';

class AppPages {
  static const initial = AppRoutes.login;

  static final routes = [
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<LoginController>(() => LoginController());
      }),
    ),
    GetPage(
      name: AppRoutes.dashboard,
      page: () => const DashboardScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<DashboardController>(() => DashboardController());
      }),
    ),
    GetPage(
      name: AppRoutes.customersList,
      page: () => const CustomersListScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<CustomersListController>(() => CustomersListController());
      }),
    ),
    GetPage(
      name: AppRoutes.orderList,
      page: () => const OrderListScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<OrderListController>(() => OrderListController());
      }),
    ),
    // Add more routes as needed
  ];
}
