import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/order_model.dart';

class OrderListController extends GetxController {
  // Observable variables
  final RxList<OrderModel> orders = <OrderModel>[].obs;
  final RxList<OrderModel> filteredOrders = <OrderModel>[].obs;
  final RxString searchQuery = ''.obs;
  final RxInt selectedNavIndex = 2.obs; // Orders List is index 2
  final RxInt currentPage = 1.obs;
  final RxInt itemsPerPage = 10.obs;
  final RxBool isLoading = false.obs;

  // Controllers
  final TextEditingController searchController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    loadOrdersData();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  // Load sample orders data
  void loadOrdersData() {
    isLoading.value = true;
    
    // Sample data matching the image
    final sampleOrders = [
      OrderModel(
        orderId: 'ORD-2024-1007',
        customerName: '<PERSON>',
        customerPhone: '+91 8522006009',
        orderDate: DateTime(2025, 6, 9),
        deliveryDate: DateTime(2025, 6, 13),
        itemCount: 3,
        amount: 5000,
        status: 'Packing',
      ),
      OrderModel(
        orderId: 'ORD-2024-1009',
        customerName: 'Krishna',
        customerPhone: '+91 8522006009',
        orderDate: DateTime(2025, 6, 12),
        deliveryDate: DateTime(2025, 6, 17),
        itemCount: 21,
        amount: 25000,
        status: 'Design Plate Approved',
      ),
      OrderModel(
        orderId: 'ORD-2024-1010',
        customerName: 'Chaitanya',
        customerPhone: '+91 8522006009',
        orderDate: DateTime(2025, 6, 15),
        deliveryDate: DateTime(2025, 6, 20),
        itemCount: 12,
        amount: 15000,
        status: 'Sorting',
      ),
      OrderModel(
        orderId: 'ORD-2024-1017',
        customerName: 'Kumar',
        customerPhone: '+91 8522006009',
        orderDate: DateTime(2025, 6, 19),
        deliveryDate: DateTime(2025, 6, 24),
        itemCount: 23,
        amount: 28000,
        status: 'Cylinder Development',
      ),
      OrderModel(
        orderId: 'ORD-2024-1018',
        customerName: 'Rajesh',
        customerPhone: '+91 8522006009',
        orderDate: DateTime(2025, 6, 20),
        deliveryDate: DateTime(2025, 6, 24),
        itemCount: 15,
        amount: 12000,
        status: 'Heating',
      ),
      OrderModel(
        orderId: 'ORD-2024-1019',
        customerName: 'Praneeth',
        customerPhone: '+91 8522006009',
        orderDate: DateTime(2025, 6, 23),
        deliveryDate: DateTime(2025, 6, 25),
        itemCount: 21,
        amount: 25000,
        status: 'On-boarding Process',
      ),
      OrderModel(
        orderId: 'ORD-2024-1020',
        customerName: 'Ravi',
        customerPhone: '+91 8522006009',
        orderDate: DateTime(2025, 6, 25),
        deliveryDate: DateTime(2025, 6, 30),
        itemCount: 22,
        amount: 25000,
        status: 'Ready to Dispatch',
      ),
      OrderModel(
        orderId: 'ORD-2024-1021',
        customerName: 'Neha',
        customerPhone: '+91 8522006009',
        orderDate: DateTime(2025, 6, 26),
        deliveryDate: DateTime(2025, 6, 28),
        itemCount: 16,
        amount: 25000,
        status: 'Packing',
      ),
      OrderModel(
        orderId: 'ORD-2024-1022',
        customerName: 'Srija',
        customerPhone: '+91 8522006009',
        orderDate: DateTime(2025, 6, 27),
        deliveryDate: DateTime(2025, 6, 29),
        itemCount: 18,
        amount: 35000,
        status: 'Packing',
      ),
    ];

    orders.value = sampleOrders;
    filteredOrders.value = sampleOrders;
    isLoading.value = false;
  }

  // Handle search
  void onSearchChanged(String query) {
    searchQuery.value = query;
    if (query.isEmpty) {
      filteredOrders.value = orders;
    } else {
      filteredOrders.value = orders.where((order) {
        return order.orderId.toLowerCase().contains(query.toLowerCase()) ||
               order.customerName.toLowerCase().contains(query.toLowerCase()) ||
               order.customerPhone.contains(query);
      }).toList();
    }
    currentPage.value = 1; // Reset to first page when searching
  }

  // Handle navigation item selection
  void selectNavItem(int index) {
    selectedNavIndex.value = index;

    switch (index) {
      case 0:
        // Dashboard
        Get.offNamed('/dashboard');
        break;
      case 1:
        // Customers List
        Get.snackbar(
          'Navigation',
          'Customers List will be implemented soon',
          snackPosition: SnackPosition.BOTTOM,
        );
        break;
      case 2:
        // Orders List - already here
        break;
    }
  }

  // Handle export
  void onExportTap() {
    Get.snackbar(
      'Export',
      'Export functionality will be implemented soon',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Handle add order
  void onAddOrderTap() {
    Get.snackbar(
      'Add Order',
      'Add Order functionality will be implemented soon',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Handle order action menu
  void onOrderActionTap(OrderModel order) {
    Get.snackbar(
      'Order Action',
      'Action for ${order.orderId} will be implemented soon',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Get paginated orders
  List<OrderModel> get paginatedOrders {
    final startIndex = (currentPage.value - 1) * itemsPerPage.value;
    final endIndex = startIndex + itemsPerPage.value;
    
    if (startIndex >= filteredOrders.length) return [];
    
    return filteredOrders.sublist(
      startIndex,
      endIndex > filteredOrders.length ? filteredOrders.length : endIndex,
    );
  }

  // Get total pages
  int get totalPages {
    return (filteredOrders.length / itemsPerPage.value).ceil();
  }

  // Change page
  void changePage(int page) {
    if (page >= 1 && page <= totalPages) {
      currentPage.value = page;
    }
  }

  // Get status color
  Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'packing':
        return const Color(0xFF3B82F6); // Blue
      case 'design plate approved':
        return const Color(0xFF10B981); // Green
      case 'sorting':
        return const Color(0xFFEC4899); // Pink
      case 'cylinder development':
        return const Color(0xFFF59E0B); // Orange
      case 'heating':
        return const Color(0xFFEF4444); // Red
      case 'on-boarding process':
        return const Color(0xFF06B6D4); // Cyan
      case 'ready to dispatch':
        return const Color(0xFF84CC16); // Lime
      default:
        return const Color(0xFF6B7280); // Gray
    }
  }
}
